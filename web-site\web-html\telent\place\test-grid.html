<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网格布局测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }

        /* 网格容器样式 */
        .grid-container {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 20px;
            margin-bottom: 40px;
        }

        /* 卡片样式 */
        .test-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .test-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .card-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .card-content {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 1400px) {
            .grid-container {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        @media (max-width: 1200px) {
            .grid-container {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 900px) {
            .grid-container {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .grid-container {
                grid-template-columns: 1fr;
            }
        }

        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>网格布局测试页面</h1>
        
        <div class="info">
            <strong>说明：</strong>此页面用于测试网格布局效果。在不同屏幕尺寸下：
            <ul>
                <li>大屏幕（>1400px）：5列</li>
                <li>中大屏幕（1200-1400px）：4列</li>
                <li>中屏幕（900-1200px）：3列</li>
                <li>小屏幕（768-900px）：2列</li>
                <li>手机屏幕（<768px）：1列</li>
            </ul>
        </div>

        <div class="grid-container" id="testGrid">
            <!-- 测试卡片将在这里生成 -->
        </div>
    </div>

    <script>
        // 生成测试卡片
        function generateTestCards() {
            const container = document.getElementById('testGrid');
            let html = '';
            
            for (let i = 1; i <= 15; i++) {
                html += `
                    <div class="test-card">
                        <div class="card-title">测试卡片 ${i}</div>
                        <div class="card-content">
                            这是第${i}个测试卡片的内容。用于验证网格布局在不同屏幕尺寸下的显示效果。
                        </div>
                    </div>
                `;
            }
            
            container.innerHTML = html;
        }

        // 页面加载完成后生成测试卡片
        document.addEventListener('DOMContentLoaded', generateTestCards);
    </script>
</body>
</html>
